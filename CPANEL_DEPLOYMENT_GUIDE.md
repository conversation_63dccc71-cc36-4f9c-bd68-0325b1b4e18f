# GG Catalog Store - cPanel Deployment Guide

## Overview
This guide covers deploying your React + Node.js catalog store application to a shared hosting provider using cPanel. For a company with tens of thousands of products, we'll also discuss when you might need to upgrade to VPS.

## Table of Contents
1. [Pre-Deployment Checklist](#pre-deployment-checklist)
2. [Shared Hosting vs VPS Decision](#shared-hosting-vs-vps-decision)
3. [cPanel Deployment Steps](#cpanel-deployment-steps)
4. [Database Setup](#database-setup)
5. [Environment Configuration](#environment-configuration)
6. [File Upload and Structure](#file-upload-and-structure)
7. [SSL Certificate Setup](#ssl-certificate-setup)
8. [Performance Optimization](#performance-optimization)
9. [Monitoring and Maintenance](#monitoring-and-maintenance)
10. [Scaling Considerations](#scaling-considerations)

## Pre-Deployment Checklist

### 1. Build Your Application
```bash
# Build the frontend for production
npm run build

# This creates optimized files in the /dist directory
```

### 2. Prepare Your Files
- ✅ Frontend build files (from `/dist` directory)
- ✅ Backend API files (entire `/api` directory)
- ✅ Database schema (`database_schema.sql`)
- ✅ Package.json and dependencies
- ✅ Environment configuration files

### 3. Test Locally
```bash
# Test production build locally
npm run build
# Serve the dist folder and test API endpoints
```

## Shared Hosting vs VPS Decision

### Shared Hosting is Suitable If:
- ✅ Product catalog under 10,000 items
- ✅ Expected concurrent users < 100
- ✅ Budget constraints (shared hosting costs $5-20/month)
- ✅ Simple deployment requirements
- ✅ Limited technical maintenance capability

### VPS is Recommended If:
- ❌ Product catalog over 10,000 items
- ❌ Expected concurrent users > 100
- ❌ Need for custom server configurations
- ❌ Real-time features (WebSocket) are critical
- ❌ Need for advanced caching (Redis)
- ❌ Multiple environments (staging, production)

**For tens of thousands of products: VPS is strongly recommended**

## cPanel Deployment Steps

### Step 1: Choose Hosting Provider
Recommended providers with Node.js support:
- **A2 Hosting** (Node.js support, good performance)
- **HostGator** (Business plans support Node.js)
- **InMotion Hosting** (VPS and shared with Node.js)
- **Namecheap** (Shared hosting with Node.js)

### Step 2: Enable Node.js in cPanel
1. Log into your cPanel
2. Look for "Node.js" or "Node.js Selector" in Software section
3. Click "Create Application"
4. Configure:
   - **Node.js Version**: 18.x or 20.x (latest LTS)
   - **Application Mode**: Production
   - **Application Root**: `public_html/api` (for API)
   - **Application URL**: `yourdomain.com/api`
   - **Application Startup File**: `index.js`

### Step 3: Domain and Subdomain Setup
Option A: Single Domain Setup
- Main site: `yourdomain.com` (serves React app)
- API: `yourdomain.com/api` (serves Node.js API)

Option B: Subdomain Setup (Recommended)
- Main site: `yourdomain.com` (serves React app)
- API: `api.yourdomain.com` (serves Node.js API)

## Database Setup

### Step 1: Create MySQL Database
1. In cPanel, go to "MySQL Databases"
2. Create database: `yourusername_ggcatalog`
3. Create database user: `yourusername_gguser`
4. Set strong password
5. Add user to database with ALL PRIVILEGES

### Step 2: Import Database Schema
1. Go to phpMyAdmin in cPanel
2. Select your database
3. Click "Import"
4. Upload your `database_schema.sql` file
5. Execute the import

### Step 3: Import Sample Data (if available)
```sql
-- If you have sample data in gg_catalog_db.sql
-- Import this file after the schema
```

## Environment Configuration

### Step 1: Create Production .env File
Create `.env.production` in your API directory:

```env
# Server Configuration
PORT=3000
NODE_ENV=production

# Session Configuration  
SESSION_SECRET=your-super-secure-production-session-key-min-32-chars

# Database Configuration
DB_HOST=localhost
DB_USER=yourusername_gguser
DB_PASSWORD=your-strong-database-password
DB_NAME=yourusername_ggcatalog

# Database Connection Pool Settings
DB_CONNECTION_LIMIT=10
DB_QUEUE_LIMIT=0

# Security Configuration
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_STRICT_MAX=50
RATE_LIMIT_ADMIN_MAX=10

# Request Size Limits
MAX_REQUEST_SIZE=10mb
```

### Step 2: Update Frontend API URLs
In your React app, update API endpoints to point to production:

```javascript
// In your API configuration file
const API_BASE_URL = process.env.NODE_ENV === 'production' 
  ? 'https://yourdomain.com/api'  // or 'https://api.yourdomain.com'
  : 'http://localhost:3000/api';
```

## File Upload and Structure

### Recommended Directory Structure in cPanel:
```
public_html/
├── index.html (from dist/)
├── main.[hash].js (from dist/)
├── vendors.[hash].js (from dist/)
├── favicon.ico
├── images/ (from dist/images/)
├── uploads/ (for product images)
└── api/
    ├── index.js
    ├── db.js
    ├── package.json
    ├── .env.production
    ├── database/
    ├── middleware/
    ├── utils/
    └── node_modules/ (installed via npm)
```

### Step 1: Upload Frontend Files
1. Upload all files from `/dist` directory to `public_html/`
2. Ensure `index.html` is in the root of `public_html/`

### Step 2: Upload Backend Files
1. Create `api` folder in `public_html/`
2. Upload entire `/api` directory contents
3. Upload `package.json` to the `api` folder

### Step 3: Install Dependencies
In cPanel Terminal or Node.js app interface:
```bash
cd public_html/api
npm install --production
```

## SSL Certificate Setup

### Step 1: Enable SSL in cPanel
1. Go to "SSL/TLS" in cPanel
2. Choose "Let's Encrypt" (free) or upload purchased certificate
3. Enable "Force HTTPS Redirect"

### Step 2: Update Application URLs
Ensure all API calls use HTTPS in production:
```javascript
const API_URL = 'https://yourdomain.com/api';
```

## Performance Optimization

### 1. Enable Gzip Compression
Add to `.htaccess` in `public_html/`:
```apache
# Enable Gzip compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>
```

### 2. Set Cache Headers
```apache
# Browser caching
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
</IfModule>
```

### 3. Database Optimization
```sql
-- Add indexes for better performance
CREATE INDEX idx_products_category ON products(category_id);
CREATE INDEX idx_products_brand ON products(brand_id);
CREATE INDEX idx_products_price ON products(price);
CREATE INDEX idx_products_rating ON products(avg_rating);
CREATE INDEX idx_products_sales ON products(total_sold);
```

## Monitoring and Maintenance

### 1. Set Up Error Logging
In your Node.js app, ensure proper logging:
```javascript
// Add to your API index.js
app.use((err, req, res, next) => {
  console.error('Error:', err);
  // Log to file in production
  res.status(500).json({ error: 'Internal server error' });
});
```

### 2. Monitor Resource Usage
- Check cPanel resource usage regularly
- Monitor database size and query performance
- Watch for memory and CPU limits

### 3. Regular Backups
- Set up automatic database backups in cPanel
- Backup uploaded images regularly
- Keep a copy of your application files

## Scaling Considerations

### When to Upgrade to VPS:

#### Performance Indicators:
- Page load times > 3 seconds
- Database queries taking > 1 second
- Frequent "resource limit exceeded" errors
- Memory usage consistently > 80%

#### Traffic Indicators:
- Concurrent users > 50-100
- Daily page views > 10,000
- API requests > 1,000/hour

#### Data Indicators:
- Product catalog > 10,000 items
- Database size > 1GB
- Image storage > 5GB

### VPS Migration Benefits:
- **Dedicated Resources**: No sharing with other websites
- **Custom Configuration**: Install Redis, configure Nginx
- **Better Performance**: SSD storage, more RAM
- **Scalability**: Easy to upgrade resources
- **Advanced Features**: Load balancing, CDN integration

### Recommended VPS Specifications:
```
Minimum VPS for 10k+ products:
- 2 CPU cores
- 4GB RAM  
- 50GB SSD storage
- 2TB bandwidth
- Cost: $20-40/month
```

## Troubleshooting Common Issues

### 1. Node.js App Won't Start
- Check Node.js version compatibility
- Verify package.json startup script
- Check error logs in cPanel

### 2. Database Connection Issues
- Verify database credentials
- Check if database user has proper permissions
- Ensure database host is correct (usually 'localhost')

### 3. CORS Errors
- Update ALLOWED_ORIGINS in .env
- Ensure frontend and API domains match

### 4. File Upload Issues
- Check directory permissions (755 for folders, 644 for files)
- Verify upload path exists
- Check file size limits

## Final Deployment Checklist

- [ ] Frontend built and uploaded to public_html/
- [ ] Backend API uploaded to public_html/api/
- [ ] Database created and schema imported
- [ ] Environment variables configured
- [ ] Node.js application started in cPanel
- [ ] SSL certificate installed and HTTPS enabled
- [ ] .htaccess configured for performance
- [ ] Database indexes created
- [ ] Error logging enabled
- [ ] Backup system configured
- [ ] Performance monitoring set up

## Conclusion

For a company with tens of thousands of products, while you can start with shared hosting to test the waters, **VPS hosting is strongly recommended** for production use. The improved performance, reliability, and scalability will provide a much better user experience and handle the load more effectively.

Start with shared hosting for development/testing, then migrate to VPS when you're ready for production deployment.
